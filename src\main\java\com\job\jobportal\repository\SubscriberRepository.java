package com.job.jobportal.repository;

import com.job.jobportal.model.Subscriber;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface SubscriberRepository extends JpaRepository<Subscriber, Long> {

    Optional<Subscriber> findByEmail(String email);

    Boolean existsByEmail(String email);

    List<Subscriber> findByIsSubscriber(Boolean isSubscriber);

    @Query("SELECT s FROM Subscriber s WHERE s.isSubscriber = :isSubscriber ORDER BY s.createdAt DESC")
    Page<Subscriber> findByIsSubscriberPaginated(@Param("isSubscriber") Boolean isSubscriber, Pageable pageable);
}
