# LinkedIn Internship Experience - Job Portal Backend Development

## Project Overview

**Project Title:** Job Portal Backend Application
**Company/Organization:** GroGlo Jobs (groglojobs.co.uk)
**Duration:** February 2025 - May 2025 (4 months)
**Role:** Backend Developer Intern
**Location:** Remote

## Project Description

Contributed to the development of a comprehensive job portal backend application using Spring Boot, serving as the core API for a job recruitment platform. The system facilitates job posting, candidate applications, company profiles, and subscription-based services for recruiters and job seekers.

## Technologies & Frameworks Used

### Core Technologies

- **Java 17** - Primary programming language
- **Spring Boot 3.3.0** - Main framework for REST API development
- **Spring Security** - Authentication and authorization
- **Spring Data JPA** - Database operations and ORM
- **MySQL** - Primary database
- **Maven** - Dependency management and build tool

### Integration & Services

- **Stripe API** - Payment processing and subscription management
- **AWS S3** - File storage for resumes and company logos
- **AWS Secrets Manager** - Configuration management
- **JWT (JSON Web Tokens)** - Secure authentication mechanism
- **OAuth2** - Social login integration (Google, Facebook)
- **Twilio** - SMS notifications and OTP services
- **Firebase** - Push notifications
- **Email Services** - SMTP integration for notifications

### Additional Tools & Libraries

- **ModelMapper** - Object mapping and data transformation
- **Lombok** - Code generation and boilerplate reduction
- **Swagger/OpenAPI** - API documentation
- **Docker** - Containerization for deployment

## Key Features Implemented

### 1. Job Application System

- **Job Application API**: Developed comprehensive job application endpoints with filtering capabilities
- **Application Status Management**: Implemented status tracking and updates for job applications
- **Candidate-Recruiter Matching**: Built queries to match candidates with job posts based on various criteria
- **Application Analytics**: Created application summary and counting functionality

### 2. Subscription & Payment Integration

- **Stripe Service Integration**: Implemented Stripe payment processing for subscription plans
- **Job Posting Limits**: Developed logic to enforce job posting limits based on subscription tiers
- **Subscription Management**: Built subscription creation, cancellation, and update functionality
- **Payment Method Management**: Implemented payment method updates and customer portal integration

### 3. Database Schema & Migration

- **Schema Design**: Created and maintained database migration scripts (schema.sql, schema2.sql)
- **Test Data Management**: Developed comprehensive test data sets for development and QA
- **Database Optimization**: Implemented efficient JPA queries with proper indexing

### 4. Job Management Features

- **Job CRUD Operations**: Implemented complete job posting creation, reading, updating, and deletion
- **Job Search & Filtering**: Built advanced search functionality with multiple filter criteria
- **Job Categorization**: Developed master data management for job categories and subcategories
- **Job Scheduling**: Implemented automated job status management and scheduling

### 5. Candidate Profile System

- **Profile Management**: Built candidate profile creation and update functionality
- **Skills & Experience Tracking**: Implemented skills validation and experience management
- **Resume Handling**: Developed resume upload and management features
- **Profile Search**: Created candidate search functionality for recruiters

### 6. SEO & Content Management

- **SEO Pages Service**: Implemented SEO page creation and search functionality
- **Newsletter System**: Developed newsletter subscription and management features
- **Content Search**: Built search functionality for programmatic pages and content

## Technical Contributions

### Database Design & Migration

- **Schema Management**: Created and maintained incremental database migration scripts (schema.sql, schema2.sql)
- **Test Data Creation**: Developed comprehensive test data sets including user accounts, job posts, and subscription data
- **Query Optimization**: Implemented complex JPA/JPQL queries with proper filtering and pagination
- **Data Relationships**: Designed efficient entity relationships and foreign key constraints

### API Development & Service Layer

- **Repository Layer**: Developed custom JPA repository methods with complex filtering capabilities
- **Service Layer Logic**: Implemented business logic for job applications, subscriptions, and profile management
- **DTO Mapping**: Created comprehensive data transfer objects and mapping logic
- **Error Handling**: Implemented proper exception handling and validation throughout the application

### Stripe Payment Integration

- **Subscription Management**: Built complete subscription lifecycle management (create, update, cancel)
- **Payment Processing**: Implemented checkout session creation and payment method management
- **Webhook Handling**: Developed webhook endpoints for payment status updates
- **Customer Management**: Created Stripe customer creation and management functionality

### Job Application & Matching System

- **Application Processing**: Developed job application submission and tracking system
- **Filtering & Search**: Implemented advanced filtering for job applications by status, time, and other criteria
- **Candidate Matching**: Built queries to match candidates with relevant job opportunities
- **Status Management**: Created application status tracking and update functionality

## Notable Achievements

### Code Quality & Implementation

- **Complex Query Development**: Implemented sophisticated JPA/JPQL queries for job application filtering and candidate matching
- **Service Layer Architecture**: Built well-structured service layer with proper separation of concerns
- **Database Migration Management**: Successfully managed incremental database schema updates and test data
- **Error Handling**: Implemented comprehensive exception handling and validation throughout the application

### Business Logic Implementation

- **Subscription Limits**: Developed job posting limit enforcement based on subscription tiers
- **Payment Integration**: Successfully integrated Stripe payment processing with webhook handling
- **Application Workflow**: Built complete job application lifecycle from submission to status tracking
- **Token Management**: Implemented JWT token updates after subscription changes

### Problem Solving & Bug Fixes

- **Newsletter Pagination**: Resolved newsletter endpoint pagination issues
- **Token Updates**: Fixed token update bugs after subscription cancellation
- **SEO Title Issues**: Resolved SEO page title uniqueness and search problems
- **Job Post Counting**: Fixed job post limit counting and validation logic

## Git Contributions

- **Total Commits:** 200+ commits over 4 months (February 2025 - May 2025)
- **Key Branches:** feature/stripe-subscription-implementation, modification/\* branches for various features
- **Commit Pattern**: Consistent commit messages with clear feature descriptions and bug fix references
- **Code Evolution**: Demonstrated iterative development with continuous improvements and refinements

## Learning Outcomes

### Technical Skills Gained

- **Spring Boot Development**: Advanced experience with Spring Boot 3.3.0, Spring Data JPA, and Spring Security
- **Payment Integration**: Hands-on experience with Stripe API for subscription management and payment processing
- **Database Management**: Proficiency in MySQL, JPA/JPQL query development, and database migration strategies
- **API Development**: RESTful API design, DTO mapping, and comprehensive error handling
- **Java Development**: Advanced Java 17 features and enterprise application development patterns

### Business Understanding

- **Subscription Models**: Understanding of trial periods, subscription tiers, and payment lifecycle management
- **Job Portal Domain**: Knowledge of recruitment workflows, candidate-employer matching, and application tracking
- **Payment Processing**: Experience with webhook handling, payment method management, and subscription billing

### Development Practices

- **Version Control**: Git workflow management with feature branches and descriptive commit messages
- **Code Quality**: Clean code principles, service layer architecture, and proper exception handling
- **Testing & Data**: Test data creation, database seeding, and development environment management
- **Problem Solving**: Debugging complex business logic, resolving integration issues, and optimizing performance

## Project Impact

- **Feature Development**: Successfully implemented core job application and subscription management features
- **Database Architecture**: Contributed to robust database design supporting complex business requirements
- **Payment System**: Delivered working Stripe integration enabling subscription-based revenue model
- **Code Quality**: Maintained high code quality standards with proper error handling and validation
- **Bug Resolution**: Resolved critical issues related to token management, pagination, and subscription limits

## Development Environment

- **Local Development**: MySQL database setup, Spring Boot application configuration
- **AWS Integration**: S3 file storage, Secrets Manager for configuration management
- **Build Tools**: Maven for dependency management and project building
- **API Documentation**: Swagger/OpenAPI for API documentation and testing

---

**Note:** This project demonstrates comprehensive full-stack backend development skills, from database design to payment integration, showcasing ability to work with modern enterprise technologies and deliver production-ready applications.
